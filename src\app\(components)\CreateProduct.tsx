import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogDescription, DialogFooter } from "@/components/ui/dialog";
import ImageUpload from "./image-upload";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import {
    Plus,
    Loader2,
    X,
} from "lucide-react";
import { Product } from "@/types/types";

type CreateProductProps = {
    setImageFile: (file: File | null) => void;
    newProduct: Partial<Product>;
    setNewProduct: (product: Partial<Product>) => void;
    recipesData: any[];
    productsData: any[];
    categoryList: any[];
    handleRecipeSelection: (recipeId: string) => void;
    selectedRecipeId: string;
    setSelectedRecipeId: (recipeId: string) => void;
    recipeIngredients: any[];
    setRecipeIngredients: (ingredients: any[]) => void;
    setUseCustomIngredients: (value: boolean) => void;
    setCustomIngredients: (ingredients: any[]) => void;
    setSelectedIngredient: (ingredient: string) => void;
    setCustomIngredientName: (name: string) => void;
    setIngredientQuantity: (quantity: string) => void;
    amountPerUnit: number;
    setAmountPerUnit: (value: number) => void;
    setShowCreateRecipeDialog: (value: boolean) => void;
    isLoading: boolean;
    handleAddCustomIngredientToList: () => void;
    setShowCustomIngredientForm: (value: boolean) => void;
    updateIngredientQuantity: (index: number, quantity: number) => void;
    handleAddProduct: () => void;
    removeIngredientFromList: (index: number) => void;
    showCustomIngredientForm: boolean;
    customIngredientForm: { name: string; amount: string; unit: string; stock: string };
    setCustomIngredientForm: (form: { name: string; amount: string; unit: string; stock: string }) => void;
    resetProductModal: () => void;
    setShowAddProductModal: (value: boolean) => void;

}

const CreateProduct = ({
    setImageFile,
    newProduct,
    setNewProduct,
    recipesData,
    productsData,
    categoryList,
    handleRecipeSelection,
    selectedRecipeId,
    setSelectedRecipeId,
    recipeIngredients,
    setRecipeIngredients,
    setUseCustomIngredients,
    setCustomIngredients,
    setSelectedIngredient,
    setCustomIngredientName,
    setIngredientQuantity,
    amountPerUnit,
    setAmountPerUnit,
    setShowCreateRecipeDialog,
    isLoading,
    handleAddCustomIngredientToList,
    setShowCustomIngredientForm,
    updateIngredientQuantity,
    handleAddProduct,
    removeIngredientFromList,
    showCustomIngredientForm,
    customIngredientForm,
    setCustomIngredientForm,
    resetProductModal,
    setShowAddProductModal,

}: CreateProductProps) => {
    return (
        <DialogContent className="sm:max-w-[800px]">
            <DialogHeader>
                <DialogTitle>Agregar Nuevo Producto</DialogTitle>
                <DialogDescription>
                    Complete los detalles del producto para agregarlo al inventario.
                </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 px-2 py-4 h-[calc(100vh-10rem)] overflow-y-auto">
                {
                    <ImageUpload
                        handleSetImageFile={setImageFile}
                        imageUrl={newProduct.image_url}
                    />
                }
                <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                        <Label htmlFor="name">Nombre</Label>
                        <Input
                            id="name"
                            value={newProduct.name}
                            onChange={(e) =>
                                setNewProduct({ ...newProduct, name: e.target.value })
                            }
                        />
                    </div>
                    <div className="space-y-2">
                        <Label htmlFor="category">Categoría</Label>
                        <Select
                            value={newProduct.category}
                            onValueChange={(value) =>
                                setNewProduct({ ...newProduct, category: value })
                            }
                        >
                            <SelectTrigger>
                                <SelectValue placeholder="Seleccionar categoría" />
                            </SelectTrigger>
                            <SelectContent>
                                {categoryList.map((category) => (
                                    <SelectItem key={category.value} value={category.value}>
                                        {category.label}
                                    </SelectItem>
                                ))}
                            </SelectContent>
                        </Select>
                    </div>
                </div>

                {/* Toggle Fields */}
                <div className="space-y-4 border rounded-lg p-4">
                    <div className="space-y-4">
                        <h3 className="text-base font-semibold">Configuración del Producto</h3>

                        {/* Liquid Product Toggle */}
                        <div className="flex items-center justify-between rounded-lg border p-3">
                            <div className="space-y-0.5">
                                <Label className="text-sm font-medium">
                                    ¿Es este un producto líquido?
                                </Label>
                                <p className="text-xs text-muted-foreground">
                                    Los productos líquidos mostrarán el total de cantidad agregada como ingrediente
                                </p>
                            </div>
                            <Switch
                                checked={newProduct.is_liquid || false}
                                onCheckedChange={(checked) =>
                                    setNewProduct({ ...newProduct, is_liquid: checked })
                                }
                            />
                        </div>

                        {/* Ingredient Type Toggle */}
                        <div className="flex items-center justify-between rounded-lg border p-3">
                            <div className="space-y-0.5">
                                <Label className="text-sm font-medium">
                                    ¿Usar este producto como ingrediente en recetas?
                                </Label>
                                <p className="text-xs text-muted-foreground">
                                    Este producto estará disponible para seleccionar como ingrediente al crear recetas
                                </p>
                            </div>
                            <Switch
                                checked={newProduct.type === "ingredient"}
                                onCheckedChange={(checked) => {
                                    setNewProduct({
                                        ...newProduct,
                                        type: checked ? "ingredient" : "product"
                                    });

                                    // Clear recipe/ingredient selections when enabling ingredient mode
                                    if (checked) {
                                        setSelectedRecipeId("");
                                        setRecipeIngredients([]);
                                        setUseCustomIngredients(false);
                                        setCustomIngredients([]);
                                        setSelectedIngredient("none");
                                        setCustomIngredientName("");
                                        setIngredientQuantity("");
                                    }
                                }}
                            />
                        </div>

                        {/* Total Amount Calculation Display - Show when ingredient type is enabled */}
                        {newProduct.type === "ingredient" && (
                            <div className="rounded-lg border p-3 bg-blue-50">
                                <div className="space-y-3">
                                    <Label className="text-sm font-medium text-blue-900">
                                        Cálculo de Cantidad Total
                                    </Label>

                                    {/* Amount per unit input */}
                                    <div className="space-y-2">
                                        <Label htmlFor="amount_per_unit" className="text-sm">
                                            Cantidad por unidad {newProduct.is_liquid ? "(ml)" : ""}
                                        </Label>
                                        <Input
                                            id="amount_per_unit"
                                            type="number"
                                            min="0"
                                            step="0.01"
                                            value={amountPerUnit === 0 ? "" : amountPerUnit}
                                            placeholder="Ej: 500 ml por botella"
                                            onChange={(e) => setAmountPerUnit(parseFloat(e.target.value) || 0)}
                                            className="h-9"
                                        />
                                    </div>

                                    <div className="grid grid-cols-2 gap-4 text-sm">
                                        <div>
                                            <span className="text-gray-600">Stock:</span>
                                            <span className="ml-2 font-medium">{newProduct.stock || 0} unidades</span>
                                        </div>
                                        <div>
                                            <span className="text-gray-600">Cantidad por unidad:</span>
                                            <span className="ml-2 font-medium">{amountPerUnit} {newProduct.is_liquid ? "ml" : ""}</span>
                                        </div>
                                    </div>
                                    <div className="pt-2 border-t border-blue-200">
                                        <span className="text-blue-900 font-semibold">
                                            Total Amount: {((newProduct.stock || 0) * amountPerUnit)}
                                            {newProduct.is_liquid ? " ml" : " unidades"}
                                        </span>
                                    </div>
                                    <p className="text-xs text-blue-700">
                                        Este valor se guardará como total_amount en la base de datos
                                    </p>
                                </div>
                            </div>
                        )}
                    </div>
                </div>

                {/* Recipe/Ingredients Selection Field */}
                <div className="space-y-4 border rounded-lg p-4">
                    <div className="flex items-center justify-between">
                        <Label className="text-base font-semibold">
                            Ingredientes (Opcional)
                        </Label>
                        <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={() => setShowCreateRecipeDialog(true)}
                        >
                            <Plus className="h-4 w-4 mr-1" />
                            Crear Receta
                        </Button>
                    </div>

                    {/* Show disabled message when ingredient toggle is enabled */}
                    {newProduct.type === "ingredient" && (
                        <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                            <p className="text-sm text-blue-800">
                                <strong>Modo Ingrediente:</strong> Los productos de tipo ingrediente no requieren recetas o ingredientes adicionales.
                                Este producto estará disponible para usar como ingrediente en otras recetas.
                            </p>
                        </div>
                    )}
                    <Select
                        value={selectedRecipeId || "no-recipe"}
                        onValueChange={handleRecipeSelection}
                        disabled={newProduct.type === "ingredient"}
                    >
                        <SelectTrigger className={newProduct.type === "ingredient" ? "opacity-50 cursor-not-allowed" : ""}>
                            <SelectValue placeholder="Seleccionar receta existente" />
                        </SelectTrigger>
                        <SelectContent>
                            <SelectItem value="no-recipe">Sin receta</SelectItem>
                            {/* Show Recipes only (filter out ingredients from recipesData) */}
                            {recipesData
                                // .filter((recipe) => recipe.type === "recipe" || recipe.is_liquid === true || recipe.type === "ingredient")
                                .map((recipe) => (
                                    <SelectItem
                                        key={`recipe-${recipe.id}`}
                                        value={recipe.id.toString()}
                                    >
                                        {recipe.name} -
                                        {recipe.type === "ingredient" && !recipe.is_liquid ? "Ingrediente" : recipe.is_liquid ? "Producto Líquido" : "Ingrediente-Tipo"}
                                    </SelectItem>
                                ))}
                            {/* Show Ingredients from productsData */}
                            {/* {productsData
                                .filter((product) => product.type === "ingredient" || product.is_liquid === true)
                                .map((ingredient) => (
                                    <SelectItem
                                        key={`ingredient-${ingredient.id}`}
                                        value={ingredient.id.toString()}
                                    >
                                        {ingredient.name} ({ingredient.category}) -
                                        {ingredient.type === "ingredient" ? "Ingrediente" : ingredient.is_liquid ? "Producto Líquido" : "Ingrediente-Tipo"}
                                    </SelectItem>
                                ))} */}
                        </SelectContent>
                    </Select>


                    {/* Ingredients Display - Works for recipes AND individual ingredients */}
                    {selectedRecipeId && selectedRecipeId !== "no-recipe" && (
                        <div className="space-y-3">
                            <div className="border border-blue-200 rounded-lg p-3">
                                <Label className="text-sm font-medium text-blue-900">
                                    Ingredientes del producto:
                                </Label>
                                <p className="text-sm text-blue-700 mt-1">
                                    Especifica cuántas unidades del producto vas a crear. Los ingredientes se descontarán automáticamente.
                                </p>
                            </div>

                            {/* Table Header */}
                            <div className="grid grid-cols-4 gap-4 p-3 border rounded-lg font-medium text-sm">
                                <div>Ingrediente</div>
                                <div className="text-center">Cantidad a crear</div>
                                <div className="text-center">Stock disponible</div>
                                <div className="text-center">Acciones</div>
                            </div>

                            {/* Show message when no ingredients loaded yet */}
                            {recipeIngredients.length === 0 && (
                                <div className="text-center py-8 text-gray-500">
                                    <p className="text-sm">
                                        Cargando ingredientes o agrega ingredientes personalizados...
                                    </p>
                                </div>
                            )}

                            {/* Ingredients Table */}
                            {recipeIngredients.map((ingredient, index) => {
                                // Calculate available stock and total needed
                                let availableStockDisplay = "";
                                let availableStockValue = 0;
                                let availableUnitsCount = 0;
                                let totalNeeded = 0;
                                let totalNeededUnit = "";
                                if (ingredient.isProductIngredient) {
                                    // For product ingredients: show actual total_amount with unit
                                    availableStockValue = ingredient.deduct_amount || 0;
                                    availableStockDisplay = `${availableStockValue} ${ingredient.unit || ""}`;
                                    // availableUnitsCount = Math.floor(availableStockValue / (ingredient.deduct_amount || 1));
                                    // totalNeeded = (ingredient.deduct_amount || 0) * (ingredient.requiredQuantity || 1);
                                    // totalNeededUnit = ingredient.unit || "";
                                } else if (ingredient.isStandardIngredient) {
                                    // For standard ingredients: show actual stock with unit (extracted from quantity)
                                    availableStockValue = !ingredient.is_liquid ? ingredient.deduct_stock || 0 : ingredient.total_amount || 0;
                                    const unit = ingredient.unit || ingredient.is_liquid ? "ml" : "";
                                    availableStockDisplay = `${availableStockValue * (parseFloat(ingredient.quantity || "1"))} ${unit}`;
                                    availableUnitsCount = ingredient.deduct_stock || 1;
                                    totalNeeded = (parseFloat(ingredient.quantity || "1") * (ingredient.requiredQuantity || 1));
                                    totalNeededUnit = unit;
                                } else {
                                    // For custom ingredients
                                    availableStockValue = ingredient.stock || 0;
                                    const unit = ingredient.unit || "";
                                    availableStockDisplay = `${availableStockValue * (parseFloat(ingredient.amount || "1"))} ${unit}`;
                                    availableUnitsCount = availableStockValue; // stock field represents available units directly
                                    totalNeeded = (parseFloat(ingredient.amount || "1") * (ingredient.requiredQuantity || 1));
                                    totalNeededUnit = unit;
                                }

                                // For product ingredients, check if there's enough total amount
                                // For other ingredients, check if there are enough units
                                let hasEnoughStock = false;

                                if (ingredient.isProductIngredient && !ingredient.is_liquid) {
                                    // Case 1: Solid product ingredient
                                    hasEnoughStock = availableStockValue >= (ingredient.requiredQuantity || 1);

                                } else if (ingredient.is_liquid && ingredient.isStandardIngredient) {
                                    // Case 2: Liquid ingredient (maybe not product ingredient)
                                    hasEnoughStock = availableStockValue >= (ingredient.requiredQuantity || 1);

                                } else {
                                    // Case 3: Standard/custom ingredient
                                    hasEnoughStock = availableUnitsCount >= (ingredient.requiredQuantity || 1);
                                }


                                return (
                                    <div
                                        key={index}
                                        className="grid grid-cols-4 gap-4 p-3 border rounded-lg items-center"
                                    >
                                        {/* Ingrediente Column */}
                                        <div>
                                            <div className="font-medium mb-1">
                                                {ingredient.name}
                                            </div>
                                            <div className="text-xs text-blue-600">
                                                {/* {ingredient.isProductIngredient && `${ingredient.deduct_amount} ${ingredient.unit} por unidad`} */}
                                                {ingredient.isStandardIngredient && !ingredient.is_liquid && `${ingredient.quantity} por unidad`}
                                                {/* {ingredient.is_liquid === true && `${ingredient.total_amount} por unidad`} */}
                                                {!ingredient.isProductIngredient && !ingredient.isStandardIngredient && `${ingredient.amount} ${ingredient.unit} por unidad`}
                                            </div>
                                        </div>

                                        {/* Cantidad a crear Column */}
                                        <div className={`flex items-center ${ingredient.isProductIngredient ? "flex-row" : "flex-col"} gap-2 text-center`}>

                                            <Input
                                                type="number"
                                                min="1"
                                                value={ingredient.requiredQuantity || 1}
                                                onChange={(e) =>
                                                    updateIngredientQuantity(
                                                        index,
                                                        parseInt(e.target.value) || 1
                                                    )
                                                }
                                                className="h-10 text-center font-medium w-full"
                                            />
                                            {ingredient.isProductIngredient ? ingredient.unit : ""}
                                            {ingredient.isStandardIngredient && (

                                                <p className="text-xs text-gray-500 mt-1">
                                                    Total necesario: {totalNeeded}{totalNeededUnit ? ` ${totalNeededUnit}` : ""}
                                                </p>

                                            )}
                                        </div>

                                        {/* Stock disponible Column */}
                                        <div className="text-center">
                                            <p className={`text-lg font-bold ${hasEnoughStock ? "text-green-600" : "text-red-600"}`}>
                                                {availableStockDisplay}
                                            </p>
                                            <p className="text-xs text-gray-500">
                                                {/* {ingredient.isProductIngredient && `Available: ${availableUnitsCount} units (${availableStockValue} ${ingredient.unit})`} */}
                                                {ingredient.isStandardIngredient && `Available: ${availableUnitsCount} units (${availableStockDisplay})`}
                                                {!ingredient.isProductIngredient && !ingredient.isStandardIngredient && !ingredient.is_liquid && `Available: ${availableStockValue} units (${availableStockDisplay})`}
                                            </p>
                                            {!hasEnoughStock && ingredient.isStandardIngredient && !ingredient.is_liquid && (
                                                <p className="text-xs text-red-600 font-medium mt-1">
                                                    {(ingredient.requiredQuantity || 1) - availableUnitsCount} unidad faltante
                                                </p>
                                            )}
                                            {!hasEnoughStock && ingredient.is_liquid && (
                                                <p className="text-xs text-red-600 font-medium mt-1">
                                                    {(ingredient.requiredQuantity || 1) - availableStockValue} ml faltante
                                                </p>
                                            )}
                                        </div>

                                        {/* Acciones Column */}
                                        <div className="text-center">
                                            <Button
                                                type="button"
                                                variant="ghost"
                                                size="sm"
                                                onClick={() => removeIngredientFromList(index)}
                                                className="text-red-600 hover:text-red-800 hover:bg-red-50"
                                            >
                                                <X className="h-4 w-4" />
                                            </Button>
                                        </div>
                                    </div>
                                );
                            })}

                        </div>
                    )}

                    {/* Add Custom Ingredient Button - Available for recipes AND individual ingredients */}
                    {selectedRecipeId && selectedRecipeId !== "no-recipe" && (
                        <div className="flex justify-center">
                            <Button
                                type="button"
                                variant="outline"
                                onClick={() => setShowCustomIngredientForm(true)}
                                className="flex items-center gap-2"
                            >
                                <Plus className="h-4 w-4" />
                                Agregar Ingrediente Personalizado
                            </Button>
                        </div>
                    )}

                    {/* Custom Ingredient Form */}
                    {showCustomIngredientForm && (
                        <div className="border rounded-lg p-4 space-y-4">
                            <div className="flex items-center justify-between">
                                <Label className="text-sm font-medium">
                                    Nuevo Ingrediente Personalizado
                                </Label>
                                <Button
                                    type="button"
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => setShowCustomIngredientForm(false)}
                                >
                                    <X className="h-4 w-4" />
                                </Button>
                            </div>

                            <div className="grid grid-cols-4 gap-4">
                                <div>
                                    <Label className="text-sm font-medium mb-2 block">
                                        Nombre
                                    </Label>
                                    <Input
                                        placeholder="Nombre del ingrediente"
                                        value={customIngredientForm.name}
                                        onChange={(e) =>
                                            setCustomIngredientForm({
                                                ...customIngredientForm,
                                                name: e.target.value
                                            })
                                        }
                                    />
                                </div>

                                <div>
                                    <Label className="text-sm font-medium mb-2 block">
                                        Cantidad por unidad
                                    </Label>
                                    <Input
                                        type="number"
                                        placeholder="Cantidad"
                                        value={customIngredientForm.amount}
                                        onChange={(e) =>
                                            setCustomIngredientForm({
                                                ...customIngredientForm,
                                                amount: e.target.value
                                            })
                                        }
                                    />
                                </div>

                                <div>
                                    <Label className="text-sm font-medium mb-2 block">
                                        Unidad
                                    </Label>
                                    <Select
                                        value={customIngredientForm.unit}
                                        onValueChange={(value) =>
                                            setCustomIngredientForm({
                                                ...customIngredientForm,
                                                unit: value
                                            })
                                        }
                                    >
                                        <SelectTrigger>
                                            <SelectValue />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="g">Grams (g)</SelectItem>
                                            <SelectItem value="kg">Kilograms (kg)</SelectItem>
                                            <SelectItem value="ml">Milliliters (mL)</SelectItem>
                                            <SelectItem value="L">Liters (L)</SelectItem>
                                            <SelectItem value="unidad">Units</SelectItem>
                                        </SelectContent>
                                    </Select>
                                </div>

                                <div>
                                    <Label className="text-sm font-medium mb-2 block">
                                        Stock Disponible
                                    </Label>
                                    <Input
                                        type="number"
                                        placeholder="Stock"
                                        value={customIngredientForm.stock}
                                        onChange={(e) =>
                                            setCustomIngredientForm({
                                                ...customIngredientForm,
                                                stock: e.target.value
                                            })
                                        }
                                    />
                                </div>
                            </div>

                            <div className="flex justify-end gap-2">
                                <Button
                                    type="button"
                                    variant="outline"
                                    onClick={() => setShowCustomIngredientForm(false)}
                                >
                                    Cancelar
                                </Button>
                                <Button
                                    type="button"
                                    onClick={handleAddCustomIngredientToList}
                                    disabled={!customIngredientForm.name || !customIngredientForm.amount || !customIngredientForm.stock}
                                >
                                    Agregar Ingrediente
                                </Button>
                            </div>
                        </div>
                    )}
                    <div className="space-y-2">
                        <Label htmlFor="description">Descripción</Label>
                        <Textarea
                            id="description"
                            value={newProduct.description}
                            onChange={(e) =>
                                setNewProduct({ ...newProduct, description: e.target.value })
                            }
                        />
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                            <Label htmlFor="purchase_price">Precio de Compra</Label>
                            <Input
                                id="purchase_price"
                                type="number"
                                value={
                                    newProduct.purchase_price === 0
                                        ? ""
                                        : newProduct.purchase_price
                                }
                                placeholder="0.00"
                                onChange={(e) =>
                                    setNewProduct({
                                        ...newProduct,
                                        purchase_price:
                                            e.target.value === "" ? 0 : Number(e.target.value),
                                    })
                                }
                            />
                        </div>
                        <div className="space-y-2">
                            <Label htmlFor="sale_price">Precio de Venta</Label>
                            <Input
                                id="sale_price"
                                type="number"
                                value={
                                    newProduct.sale_price === 0 ? "" : newProduct.sale_price
                                }
                                placeholder="0.00"
                                onChange={(e) =>
                                    setNewProduct({
                                        ...newProduct,
                                        sale_price:
                                            e.target.value === "" ? 0 : Number(e.target.value),
                                    })
                                }
                            />
                        </div>
                    </div>
                    <div className="space-y-2">
                        <Label htmlFor="stock">Stock</Label>
                        <Input
                            id="stock"
                            type="number"
                            value={newProduct.stock === 0 ? "" : newProduct.stock}
                            placeholder="0"
                            onChange={(e) =>
                                setNewProduct({
                                    ...newProduct,
                                    stock: e.target.value === "" ? 0 : Number(e.target.value),
                                })
                            }
                        />
                    </div>

                </div>
            </div>
            <DialogFooter>
                <Button
                    variant="outline"
                    onClick={() => {
                        setShowAddProductModal(false);
                        resetProductModal();
                    }}
                >
                    Cancelar
                </Button>
                <Button onClick={() => handleAddProduct()} disabled={isLoading}>
                    {isLoading ? (
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    ) : (
                        "Agregar Producto"
                    )}
                </Button>
            </DialogFooter>
        </DialogContent>
    )
};

export default CreateProduct;
